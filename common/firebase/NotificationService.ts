import notifee, {AndroidImportance, EventType} from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import {AppState} from 'react-native';
import {navigate} from '../../src/navigation/NavigationServices';
import {APP_SCREEN} from '../../src/navigation/screenType';
import store from '../../src/redux/store';

let pendingNavigation: {screen: string; params?: any} | null = null;
let lastNotificationId: string | null = null;
let appState: string = AppState.currentState;
let processedInitialNotifications: Set<string> = new Set();

AppState.addEventListener('change', (nextAppState: string) => {
  if (appState.match(/inactive|background/) && nextAppState === 'active') {
    if (pendingNavigation) {
      NotificationService.checkPendingNavigation();
    }
  }
  appState = nextAppState;
});

const NotificationService = {
  initialize: async (): Promise<void> => {
    await NotificationService.requestPermission();
    await NotificationService.createDefaultChannel();
    NotificationService.setupNotificationListeners();
  },

  requestPermission: async (): Promise<void> => {
    await notifee.requestPermission();
  },

  createDefaultChannel: async (): Promise<void> => {
    await notifee.createChannel({
      id: 'default',
      name: 'Default Channel',
      importance: AndroidImportance.HIGH,
    });
  },

  getDeviceToken: async (): Promise<string | null> => {
    try {
      const token = await messaging().getToken();

      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  },

  setupNotificationListeners: (): void => {
    messaging().onMessage(async remoteMessage => {
      console.log('Foreground message received:', remoteMessage);
      await NotificationService.displayNotification(remoteMessage);
    });

    messaging().onNotificationOpenedApp(remoteMessage => {
      NotificationService.handleNotificationTap(remoteMessage);
    });

    notifee.onForegroundEvent(({type, detail}) => {
      if (type === EventType.PRESS) {
        NotificationService.handleNotificationPress(detail);
      }
    });
  },

  checkInitialNotification: async (): Promise<void> => {
    const remoteMessage = await messaging().getInitialNotification();
    if (remoteMessage) {
      const notificationId =
        remoteMessage.messageId ||
        remoteMessage.notification?.title +
          '_' +
          (remoteMessage.sentTime || Date.now());

      if (processedInitialNotifications.has(notificationId)) {
        return;
      }
      processedInitialNotifications.add(notificationId);
      NotificationService.handleNotificationTap(remoteMessage);
    }
  },

  displayNotification: async (remoteMessage: any): Promise<void> => {
    const messageId =
      remoteMessage.messageId ||
      remoteMessage.notification?.title + '_' + Date.now();

    if (messageId === lastNotificationId) {
      return;
    }
    lastNotificationId = messageId;

    setTimeout(() => {
      if (lastNotificationId === messageId) {
        lastNotificationId = null;
      }
    }, 5000);

    await notifee.displayNotification({
      id: messageId,
      title: remoteMessage?.notification?.title,
      body: remoteMessage?.notification?.body,
      android: {
        channelId: 'default',
        pressAction: {id: 'open_notification'},
        importance: AndroidImportance.HIGH,
      },
      data: {
        target: APP_SCREEN.NOTIFICATION,
        messageId: messageId,
        ...remoteMessage.data,
      },
    });
  },

  handleNotificationTap: (remoteMessage: any): void => {
    const target = remoteMessage.data?.target || APP_SCREEN.NOTIFICATION;
    const params = remoteMessage.data ? {...remoteMessage.data} : undefined;
    NotificationService.navigateFromNotification(target, params);
  },

  handleNotificationPress: (detail: any): void => {
    const target = detail.notification?.data?.target || APP_SCREEN.NOTIFICATION;
    const params = detail.notification?.data
      ? {...detail.notification.data}
      : undefined;
    NotificationService.navigateFromNotification(target, params);
  },

  navigateFromNotification: (screen: string, data?: any): void => {
    setTimeout(() => {
      const {token} = store.getState().auth;
      const {data: profileData} = store.getState().profile;

      const isLoggedIn =
        token &&
        profileData &&
        profileData.authorities &&
        profileData.authorities.length > 0;

      if (!isLoggedIn) {
        pendingNavigation = {screen, params: data};
        return;
      }

      navigate(screen, data);
      if (pendingNavigation && pendingNavigation.screen === screen) {
        pendingNavigation = null;
      }
    }, 500);
  },

  checkPendingNavigation: async (): Promise<void> => {
    await NotificationService.checkInitialNotification();

    if (pendingNavigation) {
      const {token} = store.getState().auth;
      const {data: profileData} = store.getState().profile;
      const isLoggedIn =
        token &&
        profileData &&
        profileData.authorities &&
        profileData.authorities.length > 0;

      if (isLoggedIn) {
        navigate(pendingNavigation.screen, pendingNavigation.params);
        pendingNavigation = null;
      }
    }
  },

  clearNotificationState: (): void => {
    pendingNavigation = null;
    lastNotificationId = null;
    processedInitialNotifications.clear();
  },

  handleBackgroundEvent: async ({
    type,
    detail,
  }: {
    type: EventType;
    detail: any;
  }): Promise<void> => {
    if (type === EventType.PRESS) {
      const target =
        detail.notification?.data?.target || APP_SCREEN.NOTIFICATION;
      const params = detail.notification?.data
        ? {...detail.notification.data}
        : undefined;
      pendingNavigation = {screen: target, params};
    }
  },
};

export default NotificationService;
