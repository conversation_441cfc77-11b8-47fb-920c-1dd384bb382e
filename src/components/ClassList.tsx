import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Animated,
  LayoutRectangle,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {useTheme} from '../hooks/useTheme';
import {SvgIcons} from '../../assets/svg';
import TextApp from './TextApp';
import {HIT_SLOP, SCREEN_WIDTH} from '../utils/Scale';
import {Theme} from '../themes';
import {moderateScale, scale} from 'react-native-size-matters';

type Props = {
  options: ClassData[];
  callbackSelectOption: (item: ClassData) => void;
  valueId?: string;
};

export type ClassListRef = {
  updateClassSelect: (classId: string) => void;
};

const TOOLTIP_WIDTH = scale(200);

export const ClassList = forwardRef<ClassListRef, Props>(
  ({options, callbackSelectOption, valueId}, ref) => {
    const positionClass = valueId
      ? options.find(item => item.id === valueId)
      : options?.[0];
    const theme = useTheme();
    const buttonRef = useRef<View>(null);

    const [isModalOpen, setModalOpen] = useState(false);
    const [classSelected, setClassSelected] = useState(positionClass);
    const [componentPosition, setComponentPosition] =
      useState<LayoutRectangle | null>(null);

    const opacity = useMemo(() => new Animated.Value(0), []);

    const updateClassSelect = useCallback(
      (classId: string) => {
        const matchedClass = options?.find(item => item?.id === classId);
        if (matchedClass) {
          setClassSelected(matchedClass);
          callbackSelectOption?.(matchedClass);
        }
      },
      [options, callbackSelectOption],
    );

    useImperativeHandle(ref, () => ({
      updateClassSelect,
    }));

    const calculatePosition = useCallback(() => {
      buttonRef.current?.measureInWindow((x, y, width, height) => {
        setComponentPosition({x, y, width, height});
      });
    }, []);

    const openModal = useCallback(() => {
      calculatePosition();
      Animated.timing(opacity, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }).start(() => setModalOpen(true));
    }, [calculatePosition, opacity]);

    const hideModal = useCallback(() => {
      Animated.timing(opacity, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }).start(() => setModalOpen(false));
    }, [opacity]);

    const toggleModal = useCallback(() => {
      isModalOpen ? hideModal() : openModal();
    }, [isModalOpen, hideModal, openModal]);

    const handleSelectOptions = useCallback(
      (item: ClassData) => {
        setClassSelected(item);
        callbackSelectOption?.(item);
        hideModal();
      },
      [callbackSelectOption, hideModal],
    );

    const tooltipLeft = useMemo(() => {
      if (!componentPosition) {
        return 0;
      }
      const centerX = componentPosition.x + componentPosition.width / 2;
      const left = centerX - TOOLTIP_WIDTH / 2;
      return Math.max(0, Math.min(left, SCREEN_WIDTH - TOOLTIP_WIDTH - 20));
    }, [componentPosition]);

    return (
      <>
        <TouchableOpacity
          ref={buttonRef}
          style={styles.container}
          hitSlop={HIT_SLOP}
          onPress={toggleModal}
          onLayout={calculatePosition}>
          <SvgIcons.List />
          <TextApp
            preset="text_sm_semibold"
            text={`Class ${classSelected?.name} of ${classSelected?.orgName}`}
            style={styles.textAppMargin}
            textColor={theme.text_secondary}
            numberOfLines={1}
          />
        </TouchableOpacity>

        <Modal visible={isModalOpen} transparent>
          <TouchableOpacity
            style={{flex: 1}}
            activeOpacity={1}
            onPress={hideModal}>
            <View style={styles.overlay} />
            {componentPosition && (
              <Animated.View
                style={[
                  styles.tooltipContainer,
                  {
                    top: componentPosition.y + componentPosition.height + 6,
                    left: tooltipLeft,
                    opacity,
                  },
                ]}>
                {options.map(item => (
                  <TouchableOpacity
                    key={item.id}
                    style={[
                      styles.optionButton,
                      valueId === item?.id && styles.optionButtonSelected,
                    ]}
                    onPress={() => handleSelectOptions(item)}>
                    <TextApp
                      text={`Class ${item?.name} of ${item?.orgName}`}
                      style={styles.textAppStyle}
                      textColor="#414651"
                      numberOfLines={1}
                    />
                  </TouchableOpacity>
                ))}
              </Animated.View>
            )}
          </TouchableOpacity>
        </Modal>
      </>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D5D7DA',
    borderRadius: Theme.radius.radius_md,
    paddingHorizontal: scale(12),
    height: moderateScale(34),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    maxWidth: SCREEN_WIDTH / 2,
  },
  textAppMargin: {
    lineHeight: 18,
    marginLeft: scale(5),
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
  },
  tooltipContainer: {
    width: TOOLTIP_WIDTH,
    position: 'absolute',
    borderRadius: Theme.radius.radius_md,
    backgroundColor: '#fff',
    padding: scale(8),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  optionButton: {
    padding: 8,
    borderRadius: 8,
  },
  optionButtonSelected: {
    backgroundColor: '#FCF2E8',
  },
  textAppStyle: {
    lineHeight: 20,
  },
});
