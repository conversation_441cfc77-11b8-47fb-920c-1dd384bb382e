import {createSlice} from '@reduxjs/toolkit';
import {fetchClassByStudent, fetchUnitAssigned} from './fetchData';

interface UnitState {
  dataAssigned: UnitData[];
  classByStudent: ClassData[];
  page: number;
  total: number;
  classId: string;
  page_of_number: number;
  loading: boolean;
  error: string;
  hasMore: boolean;
}

const initialState: UnitState = {
  dataAssigned: [],
  classByStudent: [],
  page: 1,
  total: 0,
  classId: '',
  page_of_number: 10,
  loading: false,
  error: '',
  hasMore: true,
};

const UnitSlice = createSlice({
  name: 'unit',
  initialState,
  reducers: {
    resetData: () => initialState,
    saveClassId: (state, action) => {
      state.classId = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchUnitAssigned.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchUnitAssigned.fulfilled, (state, action) => {
        state.loading = false;
        state.dataAssigned = action?.payload?.data;
      })
      .addCase(fetchUnitAssigned.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchClassByStudent.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchClassByStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.classByStudent = action?.payload?.data;
      })
      .addCase(fetchClassByStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});
export const {saveClassId} = UnitSlice.actions;
export default UnitSlice.reducer;
