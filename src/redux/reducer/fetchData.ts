import {
  API_AUTH_BASE_URL,
  API_COMMUNICATION_URL,
  API_GAMIFICATION_URL,
  API_INTEGRATE_URL,
  API_MANAGEMENT_URL,
} from '@env';
import {createAsyncThunk} from '@reduxjs/toolkit';
import Toast from 'react-native-toast-message';
import {api, createAxiosInstance} from '../../api/api.ts';
import apiAuth from '../../api/apiAuth.ts';
import {END_POINTS} from '../../api/endPoints.ts';
import {UserLoginRequest} from '../../api/types.ts';
import NotificationService from '../../../common/firebase/NotificationService.ts';

const AI = createAxiosInstance(API_INTEGRATE_URL);
const API_PROFILE = createAxiosInstance(API_AUTH_BASE_URL);
const API_MANAGEMENT = createAxiosInstance(API_MANAGEMENT_URL);
const API_COMMUNICATION = createAxiosInstance(API_COMMUNICATION_URL);
const API_GAMIFICATION = createAxiosInstance(API_GAMIFICATION_URL);

export const fetchRegister = createAsyncThunk(
  'fetchAuth/register',
  async (data: UserLoginRequest, thunkAPI) => {
    try {
      const response = await apiAuth.post(END_POINTS.REGISTER, data, {
        headers: {'content-type': 'application/json'},
      });

      const {code, msgCode} = response?.data ?? {};

      if (response.status !== 200) {
        return thunkAPI.rejectWithValue('Server error');
      }

      if (code === 1) {
        Toast.show({text1: `signup.${msgCode}`, type: 'customError'});
        return thunkAPI.rejectWithValue(msgCode);
      }

      if (code === 0) {
        return response.data;
      }

      return thunkAPI.rejectWithValue('Unknown response code');
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error.message || 'Unexpected error';
      return thunkAPI.rejectWithValue(message);
    }
  },
);

export const fetchAuth = createAsyncThunk(
  'fetchAuth/auth',
  async (data: UserLoginRequest, thunkAPI) => {
    try {
      const response = await apiAuth.post(END_POINTS.AUTH, data, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
      });
      const {errorCode, error_description} = response.data;
      if (errorCode) {
        Toast.show({text1: `login.${errorCode}`, type: 'customError'});
        return thunkAPI.rejectWithValue(error_description);
      }
      setTimeout(() => {
        NotificationService.checkPendingNavigation();
      }, 500);
      return response.data;
    } catch (error: any) {
      const message =
        error?.response?.data?.error_description || error?.message;
      Toast.show({text1: message, type: 'customError'});
      return thunkAPI.rejectWithValue(message);
    }
  },
);

export const fetchUnitAssigned = createAsyncThunk(
  'fetchUnitAssigned/units',
  async (
    {page, size, classId}: {page?: number; size?: number; classId?: string},
    thunkAPI,
  ) => {
    try {
      const response = await api.get(
        `${END_POINTS.UNIT_ASSIGNED}?page=${page}&size=${size}&classId=${classId}`,
      );

      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchLessonWithUnitID = createAsyncThunk(
  'fetchLessonWithUnitID/lesson',
  async (unitId: string, thunkAPI) => {
    try {
      const response = await api.get(
        `${END_POINTS.LESSON}?exAssignId=${unitId}`,
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);
export const fetchCheckingPhonemes = createAsyncThunk(
  'question/fetchCheckingPhonemes',
  async (data: FormData, thunkAPI) => {
    try {
      const response = await AI.post(END_POINTS.CHECKING_PHONEMES, data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      console.error('Lỗi khi gửi file:', error);
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchChatbotPhonemes = createAsyncThunk(
  'chatbot/fetchChatbotPhonemes',
  async (data: FormData, thunkAPI) => {
    try {
      const response = await API_GAMIFICATION.post(
        END_POINTS.CHATBOT_PHONEMES,
        data,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      console.error('Lỗi khi gửi file:', error);
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchChatbot = createAsyncThunk(
  'chatbot/fetchChatbot',
  async (data: any, thunkAPI) => {
    try {
      const response = await API_GAMIFICATION.post(END_POINTS.CHATBOT, data);

      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);
export const fetchDoneLesson = createAsyncThunk(
  'lesson/done',
  async (data: any, thunkAPI) => {
    try {
      const response = await api.post(END_POINTS.FINISH_LESSON, data);
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchDoneLessonPractice = createAsyncThunk(
  'fetchDoneLessonPractice/done',
  async (data: any, thunkAPI) => {
    try {
      const response = await api.post(END_POINTS.FINISH_LESSON_REVIEW, data);
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchDataInfo = createAsyncThunk(
  'profile/fetchDataInfo',
  async (_, thunkAPI) => {
    try {
      const response = await API_PROFILE.get(END_POINTS.INFO);
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchDataGrade = createAsyncThunk(
  'management/fetchDataGrade',
  async (_, thunkAPI) => {
    try {
      const response = await API_MANAGEMENT.get(END_POINTS.GRADE);
      return response?.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchChooseCharactor = createAsyncThunk(
  'management/fetchChooseCharactor',
  async (data: any, thunkAPI) => {
    try {
      const response = await API_MANAGEMENT.post(
        END_POINTS.CHOOSE_CHARACTOR,
        data,
      );
      const {msg} = response?.data ?? {};
      if (response.status === 200) {
        return response?.data;
      } else {
        Toast.show({text1: msg, type: 'customError'});
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error.message || 'Unexpected error';
      Toast.show({text1: message, type: 'customError'});
      return thunkAPI.rejectWithValue(message);
    }
  },
);

export const fetchForgotPassword = createAsyncThunk(
  'security/fetchForgotPassword',
  async (data: any, thunkAPI) => {
    try {
      const response = await API_PROFILE.post(END_POINTS.FORGOT_PASSWORD, data);
      if (response.status === 200) {
        return response.data;
      } else {
        Toast.show({text1: 'Unknown response code', type: 'customError'});
        return thunkAPI.rejectWithValue('Unknown response code');
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error.message || 'Unexpected error';
      Toast.show({text1: message, type: 'customError'});
      return thunkAPI.rejectWithValue(message);
    }
  },
);

export const fetchVerifyOTP = createAsyncThunk(
  'security/fetchVerifyOTP',
  async (data: any, thunkAPI) => {
    try {
      const response = await API_PROFILE.post(END_POINTS.VERIFY_OTP, data);
      if (response.status === 200) {
        return response.data;
      } else {
        Toast.show({text1: 'Unknown response code', type: 'customError'});
        return thunkAPI.rejectWithValue('Unknown response code');
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error.message || 'Unexpected error';
      Toast.show({text1: message, type: 'customError'});
      return thunkAPI.rejectWithValue(message);
    }
  },
);

export const fetchResetPassword = createAsyncThunk(
  'security/fetchResetPassword',
  async (data: any, thunkAPI) => {
    try {
      const response = await API_PROFILE.post(END_POINTS.RESET_PASSWORD, data);
      if (response.status === 200) {
        return response.data;
      } else {
        Toast.show({text1: 'Unknown response code', type: 'customError'});
        return thunkAPI.rejectWithValue('Unknown response code');
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error.message || 'Unexpected error';
      Toast.show({text1: message, type: 'customError'});
      return thunkAPI.rejectWithValue(message);
    }
  },
);

export const fetchResetLogout = createAsyncThunk(
  'security/fetchResetLogout',
  async (_, thunkAPI) => {
    try {
      const state: any = thunkAPI.getState();
      const token = state.auth?.token;
      const response = await apiAuth.post(END_POINTS.LOG_OUT, _, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        return response.data;
      } else {
        Toast.show({text1: 'Unknown response code', type: 'customError'});
        return thunkAPI.rejectWithValue('Unknown response code');
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error.message || 'Unexpected error';
      Toast.show({text1: message, type: 'customError'});
      return thunkAPI.rejectWithValue(message);
    }
  },
);
export const fetchLessonAnswer = createAsyncThunk(
  'fetchLessonAnswer/lesson',
  async (
    data: {assignId: string; lessonId: string; isHighest: boolean},
    thunkAPI,
  ) => {
    try {
      const response = await api.get(
        `${END_POINTS.LESSON_ANSWER}?exAssignId=${data.assignId}&isGetResult=true&lessonId=${data.lessonId}&isHighest=${data.isHighest}`,
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchLessonPracticeAnswer = createAsyncThunk(
  'fetchLessonPracticeAnswer/lesson',
  async (
    data: {assignId: string; lessonId: string; isHighest: boolean},
    thunkAPI,
  ) => {
    try {
      const response = await api.get(
        `${END_POINTS.LESSON_PRACTICE_ANSWER}?exAssignId=${data.assignId}&isGetResult=true&lessonId=${data.lessonId}&isHighest=${data.isHighest}`,
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchCountMessage = createAsyncThunk(
  'fetchCountMessage/message',
  async (_, thunkAPI) => {
    try {
      const response = await API_COMMUNICATION.get(
        `${END_POINTS.COUNT_MESSAGE}`,
      );

      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchListNotification = createAsyncThunk(
  'fetchListNotification/notification',
  async ({page, size}: {page: number; size: number}, thunkAPI) => {
    try {
      const response = await API_COMMUNICATION.get(
        `${END_POINTS.NOTIFICATION}?page=${page}&size=${size}`,
      );

      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchReadMessage = createAsyncThunk(
  'fetchReadMessage/message',
  async (data: any, thunkAPI) => {
    try {
      const response = await API_COMMUNICATION.post(
        END_POINTS.READ_MESSAGE,
        data,
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchSchoolByStudent = createAsyncThunk(
  'fetchSchoolByStudent/school',
  async (_, thunkAPI) => {
    try {
      const response = await API_MANAGEMENT.get(END_POINTS.SCHOOL_BY_STUDENT);
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchClassByStudent = createAsyncThunk(
  'fetchClassByStudent/class',
  async ({page, size}: {page: number; size: number}, thunkAPI) => {
    try {
      const response = await API_MANAGEMENT.get(
        `${END_POINTS.CLASS_BY_STUDENT}?page=${page}&size=${size}`,
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchJoinClassByCode = createAsyncThunk(
  'fetchJoinClassByCode/class',
  async (data: any, thunkAPI) => {
    try {
      const response = await API_MANAGEMENT.post(
        END_POINTS.JOIN_CLASS_BY_CODE,
        data,
      );
      const {code, msg} = response?.data ?? {};

      if (response.status !== 200) {
        return thunkAPI.rejectWithValue('Server error');
      }

      if (code === 1) {
        Toast.show({text1: msg, type: 'customError'});
        return thunkAPI.rejectWithValue(msg);
      }

      if (code === 0) {
        return response.data;
      }
      return thunkAPI.rejectWithValue('Unknown response code');
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error.message || 'Unexpected error';
      Toast.show({text1: message, type: 'customError'});
      return thunkAPI.rejectWithValue(message);
    }
  },
);

export const fetchClassDetail = createAsyncThunk(
  'fetchClassDetail/class',
  async ({classId}: {classId: string}, thunkAPI) => {
    try {
      const response = await API_MANAGEMENT.get(
        `${END_POINTS.CLASS_DETAIL}/${classId}`,
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchClassLeaderBoard = createAsyncThunk(
  'fetchClassLeaderBoard/class',
  async ({classId}: {classId: string}, thunkAPI) => {
    try {
      const response = await API_MANAGEMENT.get(
        `${END_POINTS.CLASS_LEADER_BOARD}?classId=${classId}`,
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchRolePlayCard = createAsyncThunk(
  'fetchRolePlayCard/role',
  async (_, thunkAPI) => {
    try {
      const response = await API_GAMIFICATION.get(END_POINTS.ROLE_PLAY);
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);
export const fetchUpdateProfile = createAsyncThunk(
  'profile/update',
  async ({data, id}: any, thunkAPI) => {
    try {
      const response = await API_PROFILE.patch(
        END_POINTS.EDIT_PROFILE + id,
        data.data,
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchMistakeReviewLessson = createAsyncThunk(
  'fetchMistakeReviewLessson/practice',
  async (_, thunkAPI) => {
    try {
      const response = await api.get(END_POINTS.MISTAKE_REVIEW_LESSON);
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);

export const fetchSkillBoostingLessson = createAsyncThunk(
  'fetchSkillBoostingLessson/practice',
  async (_, thunkAPI) => {
    try {
      const response = await api.get(END_POINTS.SKILL_BOOSTING_LESSON);
      if (response.status === 200) {
        return response.data;
      } else {
        return thunkAPI.rejectWithValue(
          `Error ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.message || 'Unexpected error');
    }
  },
);
