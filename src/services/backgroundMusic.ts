import Sound from 'react-native-sound';
import {AppState} from 'react-native';

Sound.setCategory('Playback');

let bgSound: Sound | null = null;
let currentFile: string | null = null;
let isSoundEnabled: boolean = false;
let shouldBlockAutoResume = false;

const setBlockAutoResume = (block: boolean) => {
  shouldBlockAutoResume = block;
};

AppState.addEventListener('change', nextAppState => {
  if (nextAppState !== 'active') {
    if (bgSound) {
      bgSound.stop();
      bgSound.release();
      bgSound = null;
    }
  } else if (isSoundEnabled && currentFile && !shouldBlockAutoResume) {
    loadAndPlay(currentFile);
  }
});

const loadAndPlay = (filename: string) => {
  bgSound = new Sound(filename, Sound.MAIN_BUNDLE, error => {
    if (error) {
      return;
    }

    if (typeof bgSound?.setNumberOfLoops === 'function') {
      bgSound.setNumberOfLoops(-1);
      bgSound?.setVolume(0.6);
    }

    bgSound?.play(success => {
      if (!success) {
        console.log('Background music play failed');
      }
    });
  });
  currentFile = filename;
};

const play = (filename: string) => {
  isSoundEnabled = true;
  if (bgSound && currentFile === filename) {
    return;
  }
  if (bgSound) {
    bgSound.stop();
    bgSound.release();
    bgSound = null;
  }
  loadAndPlay(filename);
};

const pause = (manual = false) => {
  if (manual) shouldBlockAutoResume = true;
  bgSound?.pause();
};

const resume = () => {
  if (bgSound) {
    bgSound.play();
  } else if (currentFile) {
    loadAndPlay(currentFile);
  }
};

const stop = () => {
  isSoundEnabled = false;
  if (bgSound) {
    bgSound.stop();
    bgSound.release();
    bgSound = null;
  }
};

export default {
  play,
  pause,
  resume,
  stop,
  setBlockAutoResume,
};
