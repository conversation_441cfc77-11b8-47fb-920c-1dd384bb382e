import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator, TransitionPresets} from '@react-navigation/stack';
import React, {useEffect} from 'react';
import Toast, {ToastConfig} from 'react-native-toast-message';
import {toastConfig} from '../components/Toast.tsx';
import {useNetInfo} from '../hooks/useNetInfo.ts';
import {useReduxDispatch, useTypedSelector} from '../redux/store.ts';
import {isIOS} from '../utils/Scale.ts';
import AppNavigator from './AppNavigator.tsx';
import AuthNavigator from './AuthNavigator';
import {navigationRef} from './NavigationServices.ts';
import {APP_SCREEN, RootStackParamList} from './screenType.ts';
import {saveDeviceToken} from '../redux/reducer/AuthSlice.ts';
import RNBootSplash from 'react-native-bootsplash';
import {OffLineBar} from '../components/OfflineBar.tsx';
import NotificationService from '../../common/firebase/NotificationService';

const RootStack = createStackNavigator<RootStackParamList>();

const Navigation = () => {
  useNetInfo();
  const {data} = useTypedSelector(state => state.profile);
  const {token} = useTypedSelector(state => state.auth);
  const dispatch = useReduxDispatch();
  let initialRoute;

  useEffect(() => {
    const initNotifications = async () => {
      await NotificationService.initialize();
      const deviceToken = await NotificationService.getDeviceToken();
      dispatch(saveDeviceToken(deviceToken));
    };
    initNotifications();
  }, []);

  useEffect(() => {
    if (token) {
      NotificationService.checkPendingNavigation();
    }
  }, [token]);

  if (
    !data ||
    !data?.authorities ||
    data?.authorities?.length === 0 ||
    !token
  ) {
    initialRoute = APP_SCREEN.AUTH;
  } else {
    initialRoute = APP_SCREEN.UN_AUTH;
  }

  return (
    <NavigationContainer
      ref={navigationRef}
      onReady={() => RNBootSplash.hide({fade: true})}>
      <RootStack.Navigator
        screenOptions={{
          ...TransitionPresets.SlideFromRightIOS,
          headerShown: false,
          gestureEnabled: true,
        }}
        initialRouteName={initialRoute}>
        <RootStack.Screen name={APP_SCREEN.UN_AUTH} component={AppNavigator} />
        <RootStack.Screen name={APP_SCREEN.AUTH} component={AuthNavigator} />
      </RootStack.Navigator>
      <OffLineBar />
      <Toast
        config={toastConfig as ToastConfig}
        position="bottom"
        bottomOffset={isIOS ? 50 : 20}
      />
    </NavigationContainer>
  );
};

export default Navigation;
