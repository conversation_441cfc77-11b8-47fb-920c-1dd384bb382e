import {useCallback, useEffect, useRef, useState} from 'react';
import {useSharedValue, withSpring, withTiming} from 'react-native-reanimated';
import {goBack, replace} from '../navigation/NavigationServices';
import {APP_SCREEN} from '../navigation/screenType';
import {
  fetchChatbot,
  fetchChatbotPhonemes,
  fetchRolePlayCard,
} from '../redux/reducer/fetchData';
import {useReduxDispatch, useTypedSelector} from '../redux/store';
import {isAndroid} from '../utils/Scale';
import useAudioRecorder from './useAudioRecorder';
import soundService from '../services/soundService';
import {useFocusEffect} from '@react-navigation/native';
import backgroundMusic from '../services/backgroundMusic';

export const useRolePlay = (scrollCurtainRef?: React.RefObject<any>) => {
  const {recording, audioPath, startRecording, stopRecording} =
    useAudioRecorder();
  const dispatch = useReduxDispatch();

  const [rolePlayData, setRolePlayData] = useState<RolePlayCard[]>([]);
  const [modalState, setModalState] = useState({
    visible: false,
    content: null as RolePlayCard | null,
  });

  const [uiState, setUIState] = useState({
    showMission: false,
    quitModal: false,
    ccActive: false,
    ideaActive: false,
    startNowLoading: false,
    recordingLoading: false,
  });

  const [apiLoading, setApiLoading] = useState<boolean>(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [rolePlayResponse, setRolePlayResponse] = useState<any>();

  const trasnY = useSharedValue(-300);
  const opacity = useSharedValue(0);
  const speechBubbleRef = useRef<any>(null);
  const idleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {data} = useTypedSelector(state => state.profile);
  const isSoundBg = useTypedSelector(state => state.profile.isPlayBgSound);

  useFocusEffect(
    useCallback(() => {
      backgroundMusic.pause(true);
      return () => {
        backgroundMusic.setBlockAutoResume(false);
        if (isSoundBg) {
          backgroundMusic.resume();
        }
      };
    }, [isSoundBg]),
  );

  useEffect(() => {
    fetchRolePlayData();
  }, []);

  useEffect(() => {
    setTimeout(() => {
      trasnY.value = withSpring(0);
      opacity.value = withTiming(1, {duration: 300});
    }, 200);
  }, []);

  useEffect(() => {
    opacity.value = withTiming(0, {duration: 150});
    opacity.value = withTiming(1, {duration: 200});
  }, [modalState.content?.rlFigureCode]);

  const cancelIdleTimeout = () => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
      idleTimeoutRef.current = null;
      soundService?.stop();
    }
  };

  const startIdleTimeout = () => {
    cancelIdleTimeout();
    idleTimeoutRef.current = setTimeout(() => {
      soundService?.playLocalFile('choose_a_role_and_let_have_fun', false);
      speechBubbleRef?.current?.show?.(
        'Choose a role and let’s have fun!',
        false,
      );
    }, 3000);
  };

  const fetchRolePlayData = async () => {
    setApiLoading(true);
    try {
      const result = await dispatch(fetchRolePlayCard());
      if (result?.payload.data) {
        setRolePlayData(result?.payload.data);
      }
    } catch (error) {
      setRolePlayData([]);
    }
    setApiLoading(false);
  };
  const handleChooseCard = (item: RolePlayCard) => {
    setModalState(prev => ({
      ...prev,
      content: item,
    }));
    setTimeout(() => {
      speechBubbleRef?.current?.hide();
      setModalState(prev => ({
        ...prev,
        visible: true,
      }));
    }, 200);
    cancelIdleTimeout();
  };

  const handleCloseModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      visible: false,
    }));
  }, []);

  const closeQuitModal = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      quitModal: false,
    }));
  }, []);

  const handleQuit = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      quitModal: false,
    }));
    setTimeout(() => {
      goBack();
    }, 300);
  }, []);

  const onBackPress = useCallback(() => {
    if (uiState.showMission) {
      setUIState(prev => ({
        ...prev,
        quitModal: true,
      }));
    } else {
      goBack();
    }
  }, [uiState.showMission]);

  const handleStartNow = useCallback(async () => {
    handleCloseModal();
    setUIState(prev => ({...prev, startNowLoading: true}));

    const result = await dispatch(
      fetchChatbot({
        rlId: modalState?.content?.id,
      }),
    );
    const {data} = result?.payload;

    if (data) {
      soundService?.changeSource(data?.questionPath);
      setSessionId(data?.sessionId);
      setRolePlayResponse(data);

      setUIState(prev => ({
        ...prev,
        showMission: true,
        startNowLoading: false,
      }));
      trasnY.value = withTiming(-300, {duration: 300});
      speechBubbleRef?.current?.hide();
      setTimeout(() => {
        scrollCurtainRef?.current?.show();
      }, 1000);
    } else {
      setUIState(prev => ({...prev, startNowLoading: false}));
    }
  }, [
    handleCloseModal,
    dispatch,
    modalState?.content?.id,
    trasnY,
    scrollCurtainRef,
  ]);

  const handleCCPress = () => {
    if (recording) return;

    soundService?.stop();
    const nextCC = !uiState.ccActive;

    setUIState(prev => ({
      ...prev,
      ccActive: nextCC,
      ideaActive: nextCC ? false : prev.ideaActive,
    }));

    if (nextCC) {
      speechBubbleRef?.current?.show?.(
        rolePlayResponse?.question || rolePlayResponse?.questionText,
        false,
        rolePlayResponse?.questionPath,
      );
    } else {
      speechBubbleRef?.current?.hide?.();
    }
  };

  const handleIdeaPress = () => {
    if (recording) return;

    soundService?.stop();
    const nextIdea = !uiState.ideaActive;

    setUIState(prev => ({
      ...prev,
      ideaActive: nextIdea,
      ccActive: nextIdea ? false : prev.ccActive,
    }));

    if (nextIdea) {
      speechBubbleRef?.current?.show?.(rolePlayResponse?.idea || '', true);
    } else {
      speechBubbleRef?.current?.hide?.();
    }
  };

  const handleRecordingCallback = async () => {
    soundService?.stop();

    if (!recording) {
      setUIState(prev => ({...prev, ccActive: false, ideaActive: false}));
      speechBubbleRef?.current?.hide?.();
      await Promise.resolve();
      await startRecording();
      return;
    }

    setUIState(prev => ({...prev, recordingLoading: true}));
    try {
      await stopRecording();
      if (!audioPath) {
        console.warn('Không có file để upload!');
        setUIState(prev => ({...prev, recordingLoading: false}));
        return;
      }

      const formData = new FormData();
      const fileName = `audio_${Date.now()}.${isAndroid ? 'mp4' : 'm4a'}`;
      formData.append('file', {
        uri: audioPath,
        name: fileName,
        type: isAndroid ? 'audio/mp4' : 'audio/m4a',
      });
      formData.append(
        'rlRequestDto',
        JSON.stringify({
          rlId: modalState?.content?.id || '',
          sessionId: sessionId || '',
        }),
      );

      const result = await dispatch(fetchChatbotPhonemes(formData));
      if (!result?.payload) {
        setUIState(prev => ({...prev, recordingLoading: false}));
        return;
      }

      const {data} = result.payload;
      setUIState(prev => ({...prev, recordingLoading: false}));

      if (!data) {
        return;
      }

      setRolePlayResponse(data);

      if (Array.isArray(data?.score) && modalState.content?.mission) {
        const updatedMissions = modalState.content.mission.map(mission => {
          const scoreItem = data.score.find(
            (score: any) => score.index === mission.key,
          );
          return {
            ...mission,
            status: scoreItem?.status === true,
          };
        });
        const hasNewCompletion = updatedMissions.some(
          (mission, index) =>
            mission.status && !modalState.content?.mission[index]?.status,
        );
        const updateMissionState = () => {
          setModalState(prev => {
            if (!prev.content) return prev;
            return {
              ...prev,
              content: {
                ...prev.content,
                mission: updatedMissions,
              },
            };
          });
        };
        if (hasNewCompletion) {
          scrollCurtainRef?.current?.animateTaskCompleted();
          setTimeout(updateMissionState, 0);
        } else {
          updateMissionState();
        }
      }

      if (data?.status === 1) {
        await new Promise(resolve => {
          soundService.changeSourceWithCallback(data.questionPath, () => {
            resolve(undefined);
          });
        });

        setUIState(prev => ({...prev, recordingLoading: false}));
        replace(APP_SCREEN.ROLE_PLAY_COMPLETED, {
          coins: modalState?.content?.coin,
        });
      } else {
        soundService?.changeSource(data?.questionPath);
      }
    } catch (error) {
      console.error('Recording callback error:', error);
      setUIState(prev => ({...prev, recordingLoading: false}));
    }
  };

  const handleRandomCard = () => {
    fetchRolePlayData();
  };

  return {
    modalVisible: modalState.visible,
    modalContent: modalState.content,
    showMission: uiState.showMission,
    quitModal: uiState.quitModal,
    ccActive: uiState.ccActive,
    ideaActive: uiState.ideaActive,
    startNowLoading: uiState.startNowLoading,
    recordingLoading: uiState.recordingLoading,
    coins: data?.coins,
    speechBubbleRef,
    rolePlayResponse,
    trasnY,
    opacity,
    rolePlayData,
    apiLoading,
    recording,
    handleChooseCard,
    handleCloseModal,
    closeQuitModal,
    handleQuit,
    onBackPress,
    handleStartNow,
    handleCCPress,
    handleIdeaPress,
    handleRecordingCallback,
    handleRandomCard,
    startIdleTimeout,
    cancelIdleTimeout,
  };
};
