import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import ModalQuestion from '../../components/ModalQuestion.tsx';
import Pagination from '../../components/Pagination.tsx';
import SkipQuestionModal from '../../components/modal/SkipQuestionModal.tsx';

// import { useBrokenHeart } from '../../components/Brokenheart.tsx';

import {LoadResource} from '../../components/LoadResource.tsx';
import {QuotesModal} from '../../components/QuotesModal.tsx';
import {ReviewHeaderProgress} from '../../components/ReviewHeaderProgress.tsx';
import ReviewQuestionContent from '../../components/ReviewQuestionContent.tsx';
import useQuestion from '../../hooks/auth/useQuestion.ts';
import {APP_SCREEN, RootStackParamList} from '../../navigation/screenType.ts';

import {
  hiddenAnswer,
  hideModalQuestion,
  resetDataQuestion,
  setDoneQuestion,
  setHeart,
  setTimeOutQuest,
} from '../../redux/reducer/QuestionSlice.ts';
import {
  fetchDoneLessonPractice,
  fetchLessonPracticeAnswer,
} from '../../redux/reducer/fetchData.ts';
import {useReduxDispatch, useTypedSelector} from '../../redux/store.ts';
import {Theme} from '../../themes';
import soundService from '../../services/soundService.ts';
import {useFocusEffect} from '@react-navigation/native';
import backgroundMusic from '../../services/backgroundMusic.ts';

type ReviewQuestionProps = NativeStackScreenProps<
  RootStackParamList,
  APP_SCREEN.REVIEW_QUESTIONS
>;

const getModalContent = (heart: number) => ({
  title: heart === 0 ? 'Hearts all gone!' : 'Time’s over!',
  content:
    heart === 0 ? 'Come back stronger next time!' : 'Don’t worry. Try again!',
  btnText: 'Continue',
});
const ReviewQuestions: React.FC<ReviewQuestionProps> = ({route}) => {
  const {item, isDone} = route.params;
  const dispatch = useReduxDispatch();
  const {loading, dataAnswer} = useTypedSelector(state => state.lesson);
  const isSoundBg = useTypedSelector(state => state.profile.isPlayBgSound);
  const {isShowQuotes, isShowAnswer, dataFinish, answer, heart, isTimeOut} =
    useTypedSelector(state => state.question);
  // const {BrokenHeartView, showBrokenHeart} = useBrokenHeart();

  const [data, setData] = useState<any>(() => {
    const possibleData =
      item?.data || item?.questions || item?.exercises || item;
    return possibleData;
  });
  const [currentPage, setCurrentPage] = useState(0);
  const [done, setDone] = useState(false);
  const [preview, setPreview] = useState(isDone);

  const [visibleSkip, setVisibleSkip] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  useQuestion();
  const initialTimerSeconds = useMemo(() => {
    const duration = item?.duration;
    return typeof duration === 'number' && duration > 60
      ? duration
      : duration * 60;
  }, [item?.duration]);

  const currentSecondsLeftRef = useRef(initialTimerSeconds);
  const uniqueData = Array.from(
    new Map(dataFinish.map(item => [item.id, item])).values(),
  );
  const passed = uniqueData.filter(i => i.isPassExcercise)?.length || 0;
  const isTimerActive = !done && !preview;

  useFocusEffect(
    useCallback(() => {
      backgroundMusic.pause(true);
      return () => {
        backgroundMusic.setBlockAutoResume(false);
        if (isSoundBg) {
          backgroundMusic.resume();
        }
      };
    }, [isSoundBg]),
  );

  useEffect(() => {
    if (isDone) {
      setPreview(true);
      setDone(false);
    }
  }, [isDone]);

  useEffect(() => {
    if (!isDone) {
      dispatch(resetDataQuestion());
      setCurrentPage(0);
      setDone(false);
      setVisibleSkip(false);
      setDontShowAgain(false);
      setTimeout(() => {
        const heartLimit = item?.mistakeLimit || 3;
        dispatch(setHeart(heartLimit));
      }, 0);
    } else {
      console.log('Review mode - keeping existing state');
    }
  }, []);

  const handleTimerUpdate = useCallback((sec: number) => {
    currentSecondsLeftRef.current = sec;
    if (sec === 1) {
      setVisibleSkip(true);
    }
  }, []);

  const handleEnd = useCallback(() => {
    setDone(true);
    handleDone();
    setVisibleSkip(false);
    dispatch(setTimeOutQuest(false));
  }, []);

  const handleDone = useCallback(() => {
    const form = {
      id: item?.id,
      unitId: item?.unitId,
      index: item?.index,
      isDone: true,
      data: uniqueData.map(q => ({
        id: q.id,
        index: q.index,
        isPassExcercise: q.isPassExcercise,
        answerByStudent: q.answerByStudent,
      })),
      completionTime: currentSecondsLeftRef.current,
      exAssignId: item?.exAssignId,
      numOfExcercises: item?.numOfExcercises,
      deadline: item?.deadline,
      duration: item?.duration,
      attemptLimit: item?.attemptLimit,
    };
    dispatch(fetchDoneLessonPractice(form));
  }, [dataFinish]);

  const handleNextPage = useCallback(() => {
    dispatch(hideModalQuestion());
    if (currentPage < data?.length - 1) {
      setCurrentPage(prev => prev + 1);
    } else {
      setDone(true);
      setCurrentPage(0);
    }
  }, [currentPage, data?.length]);

  const handleNextPagination = useCallback((index: number) => {
    setCurrentPage(index);
    dispatch(hiddenAnswer());
  }, []);

  const fetchDataLessonAnswer = useCallback(async () => {
    try {
      const originalData =
        item?.data || item?.questions || item?.exercises || item;

      if (originalData && Array.isArray(originalData)) {
        if (dataFinish && dataFinish.length > 0) {
          const dataWithAnswers = originalData.map((question, index) => {
            const studentAnswer = dataFinish.find(
              answer => answer.id === question.id || answer.index === index,
            );

            return {
              ...question,
              answerByStudent: studentAnswer?.answerByStudent,
              isPassExcercise: studentAnswer?.isPassExcercise,
            };
          });

          setData(dataWithAnswers);
        }
      }
    } catch (error) {
      console.error('Error preparing review data:', error);
    }
  }, [item, dataFinish]);

  useEffect(() => {
    soundService?.stop();
    dispatch(setDoneQuestion(preview));
    const heartLimit = item?.mistakeLimit || 3;
    dispatch(setHeart(heartLimit));

    if (preview) {
      fetchDataLessonAnswer();
    }
  }, [preview, fetchDataLessonAnswer]);

  // useEffect(() => {
  //   if (heart === 0) {
  //     setVisibleSkip(true);
  //   }
  // }, [heart, isShowQuotes, dataFinish]);

  useEffect(() => {
    if (done) {
      handleDone();
    }
  }, [done, dataFinish?.length, data?.length]);

  if (loading) {
    return (
      <View style={styles.container}>
        <LoadResource />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {!done && (
        <ReviewHeaderProgress
          step={currentPage + 1}
          total={data.length}
          steps={data.length}
          height={18}
          isReview={preview}
          initialSeconds={initialTimerSeconds}
          completionTime={dataAnswer?.completionTime}
          isTimerActive={isTimerActive}
          onPressRight={() => {}}
          heart={heart}
          onTimerUpdate={handleTimerUpdate}
        />
      )}

      <ReviewQuestionContent
        currentPage={currentPage}
        done={done}
        data={data}
        passed={passed}
        total={item?.data?.length}
        onPreview={() => {
          setPreview(true);
          setDone(false);
          setCurrentPage(0);
        }}
        styles={styles}
        reviewItem={item}
      />

      {preview && <Pagination data={data} onPress={handleNextPagination} />}
      {!done && !preview && answer && (
        <QuotesModal
          isVisible={isShowQuotes}
          onContinue={handleNextPage}
          answer={answer}
        />
      )}
      {answer && <ModalQuestion isVisible={isShowAnswer} answer={answer} />}

      {/* <SkipQuestionModal
        {...modalContent}
        isVisible={visibleSkip || (isTimeOut && !preview)}
        onClose={() => setVisibleSkip(false)}
        onConfirm={handleEnd}
        doNotShowAgain={dontShowAgain}
        onToggleDoNotShowAgain={setDontShowAgain}
      /> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.white,
  },
  centerBox: {
    flex: 1,
    alignItems: 'center',
  },
  lottie: {
    width: 300,
    height: 300,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    bottom: 50,
    position: 'absolute',
    width: '100%',
  },
  btnFooter: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 5,
    marginRight: 8,
  },
});

export default ReviewQuestions;
