import {
  Canvas,
  Group,
  Image as SkiaImage,
  useImage,
} from '@shopify/react-native-skia';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {
  cancelAnimation,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import Header from '../../components/Header.tsx';
import { navigate } from '../../navigation/NavigationServices';
import { APP_SCREEN } from '../../navigation/screenType';
import {
  fetchMistakeReviewLessson,
  fetchSkillBoostingLessson,
} from '../../redux/reducer/fetchData';
import { useReduxDispatch } from '../../redux/store';
import backgroundMusic from '../../services/backgroundMusic';
import { Theme } from '../../themes/index.ts';
import { heightScreen, widthScreen } from '../../utils/Scale.ts';

const Practice: React.FC = () => {
  const dispatch = useReduxDispatch();
  const [loading, setLoading] = useState(false);

  const bannerImage = useImage(Theme.images.practiceHeaderBanner);
  const mistakeImage = useImage(Theme.images.practiceMistakeCard);
  const skillImage = useImage(Theme.images.practiceSkillCard);

  const bannerY = useSharedValue(-123.2);
  const cardScale = useSharedValue(0);
  const hasAnimated = useRef(false);

  const bannerTransform = useDerivedValue(
    () => [{translateY: bannerY.value}],
    [bannerY],
  );

  const cardTransform = useDerivedValue(
    () => [{scale: cardScale.value}],
    [cardScale],
  );

  useEffect(() => {
    if (hasAnimated.current) return;

    hasAnimated.current = true;

    bannerY.value = withTiming(0, {duration: 800}, finished => {
      if (finished) {
        cardScale.value = withTiming(1, {duration: 600});
      }
    });

    return () => {
      cancelAnimation(bannerY);
      cancelAnimation(cardScale);
      hasAnimated.current = false;
    };
  }, []);

  const handleMistakePress = async () => {
    backgroundMusic.pause();
    setLoading(true);
    try {
      const result = await dispatch(fetchMistakeReviewLessson()).unwrap();
      if (result?.data) {
        navigate(APP_SCREEN.REVIEW_QUESTIONS, {
          item: result.data,
          isDone: false,
        });
      }
    } catch (error) {
      console.error('Error fetching review lesson:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSkillPress = async () => {
    backgroundMusic.pause();
    setLoading(true);
    try {
      const result = await dispatch(fetchSkillBoostingLessson()).unwrap();
      if (result?.data) {
        navigate(APP_SCREEN.REVIEW_QUESTIONS, {
          item: result.data,
          isDone: false,
        });
      }
    } catch (error) {
      console.error('Error fetching review lesson:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header />
      <FastImage
        source={Theme.images.bgPractice}
        style={styles.backgroundImage}
        resizeMode="stretch"
      />
      <Canvas style={styles.animationCanvas}>
        <Group transform={[{scale: widthScreen / 375}]}>
          <Group transform={bannerTransform}>
            <SkiaImage
              image={bannerImage}
              x={48.95}
              y={0}
              width={277}
              height={123.2}
            />
          </Group>

          <Group transform={cardTransform} origin={{x: 94.35, y: 361.5}}>
            <SkiaImage
              image={mistakeImage}
              x={25}
              y={257}
              width={138.7}
              height={209}
            />
          </Group>

          <Group transform={cardTransform} origin={{x: 280.08, y: 361.5}}>
            <SkiaImage
              image={skillImage}
              x={210.73}
              y={257}
              width={138.7}
              height={209}
            />
          </Group>
        </Group>
      </Canvas>

      <Animated.View style={[styles.mistakeButton, {opacity: cardScale}]}>
        <TouchableOpacity
          style={styles.fullButton}
          onPress={handleMistakePress}
          activeOpacity={0.8}
          disabled={loading}
        />
      </Animated.View>

      <Animated.View style={[styles.skillButton, {opacity: cardScale}]}>
        <TouchableOpacity
          style={styles.fullButton}
          onPress={handleSkillPress}
          activeOpacity={0.8}
          disabled={loading}
        />
      </Animated.View>

      {loading && (
        <View style={styles.loadingOverlay} pointerEvents="auto">
          <View style={styles.loadingBox}>
            <ActivityIndicator size="large" color="#535862" />
          </View>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    position: 'absolute',
    width: widthScreen,
    height: heightScreen,
  },
  animationCanvas: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 1,
  },
  mistakeButton: {
    position: 'absolute',
    left: (25 * widthScreen) / 375,
    top: (257 * heightScreen) / 812,
    width: (138.7 * widthScreen) / 375,
    height: (209 * heightScreen) / 812,
    zIndex: 2,
  },
  skillButton: {
    position: 'absolute',
    left: (210.73 * widthScreen) / 375,
    top: (257 * heightScreen) / 812,
    width: (138.7 * widthScreen) / 375,
    height: (209 * heightScreen) / 812,
    zIndex: 2,
  },
  fullButton: {
    width: '100%',
    height: '100%',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0,0,0,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 99,
  },
  loadingBox: {
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Practice;
