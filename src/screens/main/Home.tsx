import React, {useCallback, useEffect, useState} from 'react';
import {InteractionManager, StyleSheet, View} from 'react-native';
import {scale, verticalScale} from 'react-native-size-matters';

import SvgDashboard from '../../../assets/svg/dashboard/SvgDashBoard.tsx';
import {HomeHeader} from '../../components/HeaderHome.tsx';
import {LoadResource} from '../../components/LoadResource.tsx';
import NotInClassModal from '../../components/NotInClassModal.tsx';

import {navigate} from '../../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../../navigation/screenType.ts';

import {
  fetchClassByStudent,
  fetchCountMessage,
  fetchUnitAssigned,
} from '../../redux/reducer/fetchData.ts';
import {useReduxDispatch, useTypedSelector} from '../../redux/store.ts';
import {isAndroid, widthScreen} from '../../utils/Scale.ts';
import soundService from '../../services/soundService.ts';

const Home: React.FC = () => {
  const dispatch = useReduxDispatch();
  const [ready, setReady] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [classEmpty, setClassEmpty] = useState<boolean>(true);
  const classId = useTypedSelector(vl => vl.unit.classId);

  useEffect(() => {
    const task = InteractionManager.runAfterInteractions(() => {
      setReady(true);
    });
    return () => task.cancel();
  }, []);

  useEffect(() => {
    fetchData().then();
  }, []);

  const fetchData = async () => {
    try {
      const classResult = await dispatch(
        fetchClassByStudent({page: 0, size: 20}),
      ).unwrap();
      const classList = classResult?.data || [];

      if (classList.length > 0) {
        await dispatch(
          fetchUnitAssigned({
            classId: classId ? classId : classList[0].id,
            page: 0,
            size: 60,
          }),
        );
        setClassEmpty(false);
      }

      dispatch(fetchCountMessage());
    } catch (error) {
      console.log('Fetch error:', error);
    }
  };

  const handleChangeModal = useCallback(() => {
    if (visible) {
      soundService?.stop();
    }
    setVisible(prev => !prev);
  }, [visible, soundService?.stop]);

  const handleGoToMyClass = useCallback(() => {
    soundService?.stop();
    setVisible(false);
    navigate(APP_SCREEN.MY_SCHOOL);
  }, []);

  const handleGoToPractice = useCallback(() => {
    navigate(APP_SCREEN.PRACTICE);
  }, []);

  const handleGoToFunTalk = useCallback(() => {
    navigate(APP_SCREEN.PLAY_GROUND);
  }, []);

  const handleGoToLeaderBoard = useCallback(() => {
    navigate(APP_SCREEN.LEADERBOARD);
  }, []);

  const handleGoToMission = useCallback(() => {
    if (classEmpty) {
      soundService?.playLocalFile('you_are_not_enrolled_to_any_class', false);
      handleChangeModal();
      return;
    }
    navigate(APP_SCREEN.UNIT);
  }, [classEmpty, handleChangeModal]);

  if (!ready) {
    return (
      <View style={styles.container}>
        <LoadResource />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <HomeHeader />
      <SvgDashboard
        onPressMyClass={handleGoToMyClass}
        onPressPractice={handleGoToPractice}
        onPressFunTalk={handleGoToFunTalk}
        onPressLeaderBoard={handleGoToLeaderBoard}
        onPressMission={handleGoToMission}
      />
      <NotInClassModal
        visible={visible}
        onEnterClass={handleGoToMyClass}
        onClose={handleChangeModal}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mission: {
    position: 'absolute',
    top: isAndroid ? scale(212) : scale(225),
    left: verticalScale(-2),
  },
  class: {
    position: 'absolute',
    top: isAndroid ? scale(135) : scale(138),
    right: verticalScale(-2),
  },
  practice: {
    position: 'absolute',
    top: isAndroid ? scale(320) : scale(328),
    right: verticalScale(-2),
  },
  profile: {
    width: scale(130),
    height: verticalScale(100),
    position: 'absolute',
    left: scale(10),
    bottom: verticalScale(220),
  },
  leaderboard: {
    position: 'absolute',
    top: scale(342),
    left: verticalScale(2),
  },
  talkAi: {
    position: 'absolute',
    bottom: scale(20),
    left: verticalScale(10),
  },
  lacay: {
    position: 'absolute',
    top: scale(446),
    left: verticalScale(-20),
  },
  layer2: {
    position: 'absolute',
    bottom: 0,
    width: widthScreen,
  },
});

export default Home;
