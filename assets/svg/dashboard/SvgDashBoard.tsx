import React, {memo} from 'react';
import {Canvas, Image, useImage} from '@shopify/react-native-skia';
import {Pressable, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {heightScreen, widthScreen} from '../../../src/utils/Scale';

interface ActionProps {
  onPressMyClass: () => void;
  onPressMission: () => void;
  onPressPractice: () => void;
  onPressLeaderBoard: () => void;
  onPressFunTalk: () => void;
}

const SvgDashboard: React.FC<ActionProps> = ({
  onPressPractice,
  onPressMission,
  onPressLeaderBoard,
  onPressFunTalk,
  onPressMyClass,
}) => {
  const mySchoolImage = useImage(require('./mySchool.webp'));
  const missionImage = useImage(require('./mission.webp'));
  const practiceImage = useImage(require('./practice.webp'));
  const leaderBoardImage = useImage(require('./leaderBoard.webp'));
  const funtalkImage = useImage(require('./funtalk.webp'));
  const boxgift = useImage(require('./box.webp'));
  if (
    !mySchoolImage ||
    !missionImage ||
    !practiceImage ||
    !leaderBoardImage ||
    !funtalkImage
  ) {
    return null;
  }

  const scaleX = widthScreen / 1125;
  const scaleY = heightScreen / 2436;

  return (
    <View style={{flex: 1}}>
      <FastImage
        source={require('./bgMain.webp')}
        style={{
          position: 'absolute',
          width: widthScreen,
          height: heightScreen,
        }}
        resizeMode="cover"
      />

      <Canvas style={{width: widthScreen, height: heightScreen}}>
        <Image
          image={mySchoolImage}
          x={375.05 * scaleX}
          y={488.762 * scaleY}
          width={793.44 * scaleX}
          height={438.72 * scaleY}
          fit="fill"
        />

        <Image
          image={missionImage}
          x={521.69 * scaleX}
          y={938.04 * scaleY}
          width={620.34 * scaleX}
          height={430.32 * scaleY}
          fit="fill"
        />

        <Image
          image={practiceImage}
          x={20.55 * scaleX}
          y={1290.49 * scaleY}
          width={560.16 * scaleX}
          height={441.6 * scaleY}
          fit="fill"
        />
        <Image
          image={boxgift}
          x={610.55 * scaleX}
          y={1460.49 * scaleY}
          width={160.16 * scaleX}
          height={116.6 * scaleY}
          fit="fill"
        />
        <Image
          image={boxgift}
          x={60.55 * scaleX}
          y={1860.49 * scaleY}
          width={160.16 * scaleX}
          height={116.6 * scaleY}
          fit="fill"
        />
        <Image
          image={leaderBoardImage}
          x={67.69 * scaleX}
          y={750.922 * scaleY}
          width={330.96 * scaleX}
          height={463.44 * scaleY}
          fit="fill"
        />

        <Image
          image={funtalkImage}
          x={247.09 * scaleX}
          y={1756.52 * scaleY}
          width={776.64 * scaleX}
          height={502.8 * scaleY}
          fit="fill"
        />
      </Canvas>

      <Pressable
        style={{
          position: 'absolute',
          left: 375.05 * scaleX,
          top: 488.762 * scaleY,
          width: 793.44 * scaleX,
          height: 438.72 * scaleY,
        }}
        onPress={onPressMyClass}
      />

      <Pressable
        style={{
          position: 'absolute',
          left: 521.69 * scaleX,
          top: 938.04 * scaleY,
          width: 620.34 * scaleX,
          height: 430.32 * scaleY,
        }}
        onPress={onPressMission}
      />

      <Pressable
        style={{
          position: 'absolute',
          left: 20.55 * scaleX,
          top: 1290.49 * scaleY,
          width: 560.16 * scaleX,
          height: 441.6 * scaleY,
        }}
        onPress={onPressPractice}
      />

      <Pressable
        style={{
          position: 'absolute',
          left: 67.69 * scaleX,
          top: 750.922 * scaleY,
          width: 330.96 * scaleX,
          height: 463.44 * scaleY,
        }}
        onPress={onPressLeaderBoard}
      />

      <Pressable
        style={{
          position: 'absolute',
          left: 247.09 * scaleX,
          top: 1756.52 * scaleY,
          width: 776.64 * scaleX,
          height: 502.8 * scaleY,
        }}
        onPress={onPressFunTalk}
      />
    </View>
  );
};

export default memo(SvgDashboard);
